module.exports = [
"[project]/app/layout.tsx [app-rsc] (ecmascript)", ((__turbopack_context__, module, exports) => {

// / import type { Metadata } from "next";
// import { Inter } from "next/font/google";
// import "./globals.css";
// const inter = Inter({
//   subsets: ["latin"],
//   weight: ["400", "500", "600","700","800","900"]});
// export const metadata: Metadata = {
//   title: "MY-Portfolio",
//   description: "A perfeect portfolio website website that showcase my work",
// };
// export default function RootLayout({
//   children,
// }: Readonly<{
//   children: React.ReactNode;
// }>) {
//   return (
//     <html lang="en">
//       <body
//         className={`${inter.className} antialiased bg-neutral-100 dark:bg-neutral-700`}
//       >
//         {children}
//       </body>
//     </html>
//   );
// }
}),
];

//# sourceMappingURL=app_layout_tsx_271801d7._.js.map