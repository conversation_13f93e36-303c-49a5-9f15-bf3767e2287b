@import "tailwindcss";
@import "tw-animate-css";

/* @custom-variant dark {&:is {.dark *}}; */

/* @theme inline {
  --color-primary: var(--color-neutral-800);
  --color-primary: var(--color-neutral-800);
}
.dark{
  --color-primary: var(--color-neutral-100);
} */



:root {
  --color-primary: #1f2937;
  --background: #ffffff;
  --foreground: #000000;
}

.dark {
  --color-primary: #f3f4f6;
  --background: #000000;
  --foreground: #ffffff;
}

body {
  background-color: var(--background);
  color: var(--foreground);
}


