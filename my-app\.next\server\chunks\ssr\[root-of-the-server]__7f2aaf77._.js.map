{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/MY-Website/my-app/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen flex items-start justify-start \">\n      <h1 className =\"text-2xl md:text-4xl font-bold tracking-tight text-primary\">\n        Hello, World\n      </h1>\n      <p className = \"text-primary text-sm md:text-base\">\n        I am Software Developer with a passioan for creating beautiful and user-friendly interfaces. \n        I have a strong background in web development and a deep understanding of front-end and back-end technologies.  \n      </p>\n    </div>\n  );\n}\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import Image from \"next/image\";\n// // import {Inter} from \"next/font/google\";\n// import { Container } from \"./components/container\";\n\n\n// export default function Home() {\n//   return (\n//     <div className=\"min-h-screen flex items-start justify-start \">\n//       <Container className = \"main-h-screen\">\n//         <h1 className =\"text-2xl md:text-4xl font-bold tracking-tight text-primary\">\n//           Hello, World\n//         </h1>\n//         <p className = \"text-primary text-sm md:text-base\">\n//           I am Software Developer with a passioan for creating beautiful and user-friendly interfaces. \n//           I have a strong background in web development and a deep understanding of front-end and back-end technologies.  \n//         </p>\n//       </Container>\n//     </div>\n//   );\n// }\n"], "names": [], "mappings": ";;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAW;0BAA6D;;;;;;0BAG5E,8OAAC;gBAAE,WAAY;0BAAoC;;;;;;;;;;;;AAMzD,EAgDA,kCAAkC;CAClC,6CAA6C;CAC7C,sDAAsD;CAGtD,mCAAmC;CACnC,aAAa;CACb,qEAAqE;CACrE,gDAAgD;CAChD,uFAAuF;CACvF,yBAAyB;CACzB,gBAAgB;CAChB,8DAA8D;CAC9D,0GAA0G;CAC1G,6HAA6H;CAC7H,eAAe;CACf,qBAAqB;CACrB,aAAa;CACb,OAAO;CACP,IAAI", "debugId": null}}]}