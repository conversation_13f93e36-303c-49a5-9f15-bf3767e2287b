{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/MY-Website/my-app/app/layout.tsx"], "sourcesContent": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// / import type { Metada<PERSON> } from \"next\";\n// import { Inter } from \"next/font/google\";\n// import \"./globals.css\";\n\n// const inter = Inter({\n//   subsets: [\"latin\"],\n//   weight: [\"400\", \"500\", \"600\",\"700\",\"800\",\"900\"]});\n\n\n// export const metadata: Metadata = {\n//   title: \"MY-Portfolio\",\n//   description: \"A perfeect portfolio website website that showcase my work\",\n// };\n\n// export default function RootLayout({\n//   children,\n// }: Readonly<{\n//   children: React.ReactNode;\n// }>) {\n//   return (\n//     <html lang=\"en\">\n//       <body\n//         className={`${inter.className} antialiased bg-neutral-100 dark:bg-neutral-700`}\n//       >\n//         {children}\n//       </body>\n//     </html>\n//   );\n// }\n"], "names": [], "mappings": "AA6CA,0CAA0C;AAC1C,4CAA4C;AAC5C,0BAA0B;AAE1B,wBAAwB;AACxB,wBAAwB;AACxB,uDAAuD;AAGvD,sCAAsC;AACtC,2BAA2B;AAC3B,+EAA+E;AAC/E,KAAK;AAEL,uCAAuC;AACvC,cAAc;AACd,gBAAgB;AAChB,+BAA+B;AAC/B,QAAQ;AACR,aAAa;AACb,uBAAuB;AACvB,cAAc;AACd,0FAA0F;AAC1F,UAAU;AACV,qBAAqB;AACrB,gBAAgB;AAChB,cAAc;AACd,OAAO;AACP,IAAI", "debugId": null}}]}