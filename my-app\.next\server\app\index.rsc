1:"$Sreact.fragment"
2:I[39756,["/_next/static/chunks/060f9a97930f3d04.js"],"default"]
3:I[37457,["/_next/static/chunks/060f9a97930f3d04.js"],"default"]
4:I[97367,["/_next/static/chunks/060f9a97930f3d04.js"],"OutletBoundary"]
6:I[11533,["/_next/static/chunks/060f9a97930f3d04.js"],"AsyncMetadataOutlet"]
8:I[97367,["/_next/static/chunks/060f9a97930f3d04.js"],"ViewportBoundary"]
a:I[97367,["/_next/static/chunks/060f9a97930f3d04.js"],"MetadataBoundary"]
b:"$Sreact.suspense"
d:I[68027,[],"default"]
:HL["/_next/static/chunks/3e27f796c662e9e7.css","style"]
:HL["/_next/static/media/83afe278b6a6bb3c-s.p.3a6ba036.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
0:{"P":null,"b":"6MCdiWNjzYcSQEO2p1yft","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/chunks/3e27f796c662e9e7.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"inter_64afbe89-module__MivBTa__className antialiased bg-neutral-100 dark:bg-neutral-700","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen flex items-start justify-start ","children":["$","div",null,{"className":"max-w-4xl mx-auto bg-white border dark:bg-black p-4 md:p-10 min-h-screen","children":[["$","h1",null,{"className":"text-2xl md:text-4xl font-bold tracking-tight text-primary","children":"Hello, World"}],["$","p",null,{"className":"text-primary text-sm md:text-base","children":"I am Software Developer with a passioan for creating beautiful and user-friendly interfaces. I have a strong background in web development and a deep understanding of front-end and back-end technologies."}]]}]}],null,["$","$L4",null,{"children":["$L5",["$","$L6",null,{"promise":"$@7"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$L8",null,{"children":"$L9"}],["$","meta",null,{"name":"next-size-adjust","content":""}]],["$","$La",null,{"children":["$","div",null,{"hidden":true,"children":["$","$b",null,{"fallback":null,"children":"$Lc"}]}]}]]}],false]],"m":"$undefined","G":["$d",[["$","link","0",{"rel":"stylesheet","href":"/_next/static/chunks/3e27f796c662e9e7.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]]],"s":false,"S":true}
9:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
5:null
e:I[27201,["/_next/static/chunks/060f9a97930f3d04.js"],"IconMark"]
7:{"metadata":[["$","title","0",{"children":"MY-Portfolio"}],["$","meta","1",{"name":"description","content":"A perfeect portfolio website website that showcase my work"}],["$","link","2",{"rel":"icon","href":"/favicon.ico?favicon.0b3bf435.ico","sizes":"256x256","type":"image/x-icon"}],["$","$Le","3",{}]],"error":null,"digest":"$undefined"}
c:"$7:metadata"
