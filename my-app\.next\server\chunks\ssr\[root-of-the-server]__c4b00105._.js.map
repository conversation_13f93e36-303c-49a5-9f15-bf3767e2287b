{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/MY-Website/my-app/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/MY-Website/my-app/app/components/container.tsx"], "sourcesContent": ["import {cn} from \"@/lib/utils\";\r\nimport React from 'react';\r\n\r\nexport const Container = ({\r\n  children,\r\n  className,\r\n}:{\r\n    children:React.ReactNode,\r\n    className?:string\r\n  }) => {\r\n  return (\r\n    <div className={cn(\"max-w-4xl mx-auto bg-white text-black border dark:bg-black p-4 md:p-10\",className)}>\r\n        {children}\r\n    </div>\r\n  );\r\n};\r\n\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGO,MAAM,YAAY,CAAC,EACxB,QAAQ,EACR,SAAS,EAIR;IACD,qBACE,8OAAC;QAAI,WAAW,IAAA,kHAAE,EAAC,0EAAyE;kBACvF;;;;;;AAGT", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/MY-Website/my-app/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\n// import {Inter} from \"next/font/google\";\nimport { Container } from \"./components/container\";\n\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen flex items-start justify-start \">\n      <Container className = \"min-h-screen\">\n        <h1 className =\"text-2xl md:text-4xl font-bold tracking-tight text-primary\">\n          Hello, World\n        </h1>\n        <p className = \"text-primary text-sm md:text-base\">\n          I am Software Developer with a passioan for creating beautiful and user-friendly interfaces. \n          I have a strong background in web development and a deep understanding of front-end and back-end technologies.  \n        </p>\n      </Container>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA,0CAA0C;AAC1C;;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,4IAAS;YAAC,WAAY;;8BACrB,8OAAC;oBAAG,WAAW;8BAA6D;;;;;;8BAG5E,8OAAC;oBAAE,WAAY;8BAAoC;;;;;;;;;;;;;;;;;AAO3D", "debugId": null}}]}